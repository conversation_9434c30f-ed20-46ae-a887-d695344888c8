<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElPopconfirm,
  ElSelect,
  ElSwitch,
  ElTag,
  ElTooltip
} from 'element-plus';
import type { TableColumnCtx } from 'element-plus';
import { useBoolean } from '@sa/hooks';
import {
  changeUserStatus,
  compensateUserTime,
  fetchOpenaiList,
  fetchSubTypeList,
  fetchUserPage,
  removeUserBatchByIds,
  resetUserPassword
} from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { usePermission } from '@/hooks/business/auth';
import UserSearch from './modules/user-search.vue';
import TableHeaderOperation from './modules/user-table-header-operation.vue';
import UserOperateModal from './modules/user-operate-modal.vue';
import BatchCreateModal from './modules/batch-create-modal.vue';

// Add type definition for search params with sorting
interface UserSearchParams extends Api.Common.CommonSearchParams {
  sortProp?: string;
  sortOrder?: 'asc' | 'desc' | '';
}

const { hasPermission } = usePermission();

// Define the proper type for your table data
interface User {
  id: string;
  userToken: string;
  email: string;
  affRate: string;
  lastActiveTime: string;
  dailyConversationCount: number;
  dailyClaudeConversationCount: number;
  userType: number;
  loginType: number;
  status: number;
  clientIp: string;
  affCode: string;
  invitor: string;
  affCount: number;
  affQuota: string;
  modelLimits: Record<string, any>;
  expireTime: string;
  plusExpireTime: string;
  claudeExpireTime: string;
  claudeProExpireTime: string;
  grokExpireTime: string;
  grokSuperExpireTime: string;
  createTime: string;
  updateTime: string;
  remark: string;
}

type TableFormatter = (row: any, column: TableColumnCtx<any>, cellValue: any, index: number) => any;

const operateType = ref<'create' | 'update'>('create');
const { bool: visible, setTrue: openModal } = useBoolean();
const { bool: batchVisible, setTrue: openBatchModal } = useBoolean();
const { bool: compensateVisible, setTrue: openCompensateModal } = useBoolean();
const currentData = ref<any>(null);
const options = ref<any[]>([]);
const plans = ref<any[]>([]);
const compensateForm = ref({
  type: '',
  minutes: 0
});
const compensateLoading = ref(false);
const compensateTypes = [
  { label: '普通GPT权益', value: '1' },
  { label: '高级GPT权益', value: '2' },
  { label: '普通Claude权益', value: '3' },
  { label: '高级Claude权益', value: '4' },
  { label: '普通Grok权益', value: '5' },
  { label: '高级Grok权益', value: '6' }
];
const { columns, columnChecks, data, loading, pagination, getData, searchParams, resetSearchParams, getDataByPage } =
  useTable<UI.TableApiFn<User>>({
    apiFn: fetchUserPage,
    columns: () => [
      { type: 'selection', width: 48 },
      { type: 'index', label: '序号', align: 'center', width: 60 },
      { prop: 'userToken', label: '用户名', align: 'center', minWidth: 300, showOverflowTooltip: true },
      { prop: 'email', label: '邮箱', align: 'center', minWidth: 200, showOverflowTooltip: true },
      { prop: 'tenantName', label: '所属分站', align: 'center', minWidth: 100, showOverflowTooltip: true },
      { prop: 'affRate', label: '返现比例', align: 'center', minWidth: 100 },
      { prop: 'lastActiveTime', label: '使用时间', sortable: 'custom', align: 'center', minWidth: 180 },
      { prop: 'dailyConversationCount', label: 'GPT次数', sortable: 'custom', align: 'center', minWidth: 100 },
      { prop: 'dailyClaudeConversationCount', label: 'Claude次数', sortable: 'custom', align: 'center', minWidth: 120 },
      {
        prop: 'userType',
        label: '用户类型',
        align: 'center',
        minWidth: 100,
        sortable: 'custom',
        formatter: (row: User) => {
          if (row.userType === 1) return <ElTag type="primary">普通用户</ElTag>;
          if (row.userType === 2) return <ElTag type="success">免费用户</ElTag>;
          if (row.userType === 3) return <ElTag type="warning">付费用户</ElTag>;
          if (row.userType === 4) return <ElTag type="danger">Pro用户</ElTag>;
          return <span>--</span>;
        }
      },
      {
        prop: 'loginType',
        label: '登录类型',
        align: 'center',
        sortable: 'custom',
        width: 120,
        formatter: (row: any) => {
          const typeMap = {
            1: { type: 'primary', text: '账号密码登录' },
            2: { type: 'success', text: '授权码登录' }
          };
          const config = typeMap[row.loginType] || { type: 'warning', text: '未知' };
          return <ElTag type={config.type}>{config.text}</ElTag>;
        }
      },
      { prop: 'clientIp', label: '客户端IP', align: 'center', width: 120, showOverflowTooltip: true },
      { prop: 'affCode', label: '邀请码', align: 'center', width: 100 },
      {
        prop: 'status',
        label: '状态',
        minWidth: 100,
        align: 'center',
        sortable: 'custom',
        formatter: (row: User) => (
          <ElSwitch v-model={row.status} active-value={1} inactive-value={0} onChange={() => changeStatus(row)} />
        )
      },
      {
        prop: 'modelLimits',
        label: '模型限制',
        align: 'center',
        sortable: 'custom',
        width: 120,
        formatter: ((row: any) => (
          <ElTooltip effect="dark" content={getModelLimitsTooltip(row.modelLimits)} placement="top" raw-content>
            <span class="cursor-pointer text-xs">查看详情</span>
          </ElTooltip>
        )) as TableFormatter
      },
      { prop: 'invitor', label: '邀请人', align: 'center', sortable: 'custom', width: 180, showOverflowTooltip: true },
      { prop: 'affCount', label: '推广人数', align: 'center', sortable: 'custom', width: 100 },
      { prop: 'affQuota', label: '可提现金额', align: 'center', sortable: 'custom', width: 120 },
      { prop: 'expireTime', label: '普通时间', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'plusExpireTime', label: 'Plus时间', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'claudeExpireTime', label: 'Claude时间', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'claudeProExpireTime', label: 'Pro时间', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'grokExpireTime', label: 'Grok时间', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'grokSuperExpireTime', label: 'Super时间', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'remark', label: '备注', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'createTime', label: '创建时间', align: 'center', sortable: 'custom', width: 180 },
      { prop: 'updateTime', label: '更新时间', align: 'center', sortable: 'custom', width: 180 },
      {
        prop: 'operate',
        label: '操作',
        align: 'center',
        fixed: 'right',
        width: 200,
        formatter: (row: any) => (
          <div class="flex-center gap-4px">
            {hasPermission('user/update') && (
              <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
                {$t('common.edit')}
              </ElButton>
            )}
            {
              <ElPopconfirm title="确认重置密码？" onConfirm={() => handleResetPassword(row.id)}>
                {{
                  reference: () => (
                    <ElButton type="warning" plain size="small">
                      重置
                    </ElButton>
                  )
                }}
              </ElPopconfirm>
            }
            <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelClick(row.id)}>
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small" v-show={hasPermission('user/delete')}>
                    {$t('common.delete')}
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });
// Define model types array
const modelTypes = [
  { id: 'gpt-4o', name: 'gpt-4o' },
  { id: 'o3', name: 'o3' },
  { id: 'o1-pro', name: 'o1-pro' },
  { id: 'o4-mini', name: 'o4-mini' },
  { id: 'o4-mini-high', name: 'o4-mini-high' },
  { id: 'third-models', name: 'third-models' },
  { id: 'gpt-4-5', name: 'gpt-4-5' },
  { id: 'grok-3', name: 'grok-3' },
  { id: 'claude-3.5', name: 'claude' },
  { id: 'research', name: 'research' }
];

function getModelLimitsTooltip(modelLimits: Record<string, any>) {
  if (!modelLimits) return '暂无限制';

  // 时间周期转换
  const periodMap: Record<string, string> = {
    '1w': '每周',
    '3h': '每3小时',
    '5h': '每5小时',
    '1s': '每秒',
    '1m': '每分钟',
    '1h': '每小时',
    '1d': '每天',
    '1y': '每年',
    '': '无限制'
  };

  // 处理每个模型的限制
  const lines = Object.entries(modelLimits)
    .map(([model, config]) => {
      if (typeof config === 'object' && config !== null) {
        const limit = config.limit;
        const period = config.per;
        const displayPeriod = periodMap[period] || period;

        if (limit === 0 || limit === '0') {
          return `${model}: 禁止使用`;
        } else if (!limit || limit === null) {
          return `${model}: 无限制`;
        }

        return `${model}: ${displayPeriod} ${limit}次`;
      }
      return `${model}: ${JSON.stringify(config)}`;
    })
    .join('<br>');

  return lines || '暂无限制';
}

async function changeStatus(row: User) {
  try {
    await changeUserStatus({ id: row.id, status: row.status });
    ElMessage.success('状态修改成功');
    getData();
  } catch {
    ElMessage.error('状态修改失败');
  }
}

function handleEdit(row: User) {
  operateType.value = 'update';
  currentData.value = { ...row, password: '' };
  openModal();
}

async function handleDelClick(id: string) {
  await removeUserBatchByIds([id]);
  ElMessage({
    message: '删除成功',
    type: 'success',
    plain: true
  });
  getData();
}

async function handleResetPassword(id: string) {
  try {
    await resetUserPassword(id);
    ElMessage({
      message: '密码重置成功，已被重置为123456',
      type: 'success',
      plain: true
    });
  } catch {
    ElMessage({
      message: '密码重置失败',
      type: 'error',
      plain: true
    });
  }
}

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data as any, getData);

async function handleBatchDelete() {
  await removeUserBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
}

function handleAdd() {
  operateType.value = 'create';
  currentData.value = {
    modelLimits: {}
  };
  openModal();
}

function handleBatchAdd() {
  openBatchModal();
}

async function initSubtypeList() {
  const res = await fetchSubTypeList();
  plans.value = res?.data;
}

// Load data on mount
onMounted(() => {
  getData();
  loadGptList();
  initSubtypeList();
});

// Add these data loading functions
async function loadGptList() {
  try {
    const res = await fetchOpenaiList();
    options.value = res.data || [];
  } catch {
    ElMessage.error('加载GPT列表失败');
  }
}

// Modify the handleSortChange function
function handleSortChange({ prop, order }: { prop: string; order: string }) {
  const params = searchParams as UserSearchParams;
  params.sortProp = prop;
  params.sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
  getData();
}

function handleOpenCompensate() {
  compensateForm.value = { type: '', minutes: 0 };
  openCompensateModal();
}

async function handleCompensateConfirm() {
  if (!compensateForm.value.type || !compensateForm.value.minutes) {
    ElMessage.warning('请选择类型并填写分钟数');
    return;
  }
  compensateLoading.value = true;
  try {
    await compensateUserTime({
      type: compensateForm.value.type,
      minutes: compensateForm.value.minutes
    });
    ElMessage.success('补偿成功');
    compensateVisible.value = false;
    getData();
  } catch {
    ElMessage.error('补偿失败');
  } finally {
    compensateLoading.value = false;
  }
}
</script>

<template>
  <div class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @batch-add="handleBatchAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
              @compensate="handleOpenCompensate"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @sort-change="handleSortChange"
          @selection-change="(selection: User[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>

    <!-- 用户操作弹窗 -->
    <UserOperateModal
      v-model:visible="visible"
      :operate-type="operateType"
      :data="currentData"
      :options="options"
      :model-types="modelTypes"
      @submitted="getData"
    />

    <!-- 批量创建弹窗 -->
    <BatchCreateModal v-model:visible="batchVisible" :plans="plans" @submitted="getData" />

    <ElDialog v-model="compensateVisible" title="时长补偿" width="400px" :close-on-click-modal="false">
      <p class="m-4 text-red font-bold">权益时间为空的不参与补偿</p>
      <ElForm :model="compensateForm" label-width="80px">
        <ElFormItem label="类型" required>
          <ElSelect v-model="compensateForm.type" placeholder="请选择类型">
            <ElOption v-for="item in compensateTypes" :key="item.value" :label="item.label" :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="分钟数" required>
          <ElInputNumber v-model="compensateForm.minutes" :min="1" placeholder="请输入分钟数" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="compensateVisible = false">取消</ElButton>
        <ElButton type="primary" :loading="compensateLoading" @click="handleCompensateConfirm">确认</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
