<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON><PERSON>utton, ElPopconfirm } from 'element-plus';
import { fetchRiskControlPage, removeRiskControlBatchByIds } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import RiskcontrolSearch from './modules/riskcontrol-search.vue';

// Define the proper type for your table data
interface RiskControlItem {
  id: number;
  username: string;
  prompt: string;
  keyword: string;
  createdAt: string;
  updatedAt: string;
  // Add other properties as needed
}

const wrapperRef = ref<HTMLElement | null>(null);

const { columns, columnChecks, data, loading, pagination, getData, searchParams, getDataByPage, resetSearchParams } =
  useTable<RiskControlItem>({
    apiFn: fetchRiskControlPage,
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'index', label: $t('common.index'), width: 64 },
      { prop: 'username', label: '用户名', minWidth: 200 },
      { prop: 'prompt', label: '对话内容', minWidth: 300, showOverflowTooltip: true },
      { prop: 'keyword', label: '敏感词', minWidth: 200, showOverflowTooltip: true },
      { prop: 'createdAt', label: '创建时间', width: 180 },
      { prop: 'updatedAt', label: '更新时间', width: 180 },
      {
        prop: 'operate',
        label: $t('common.operate'),
        align: 'center',
        fixed: 'right',
        formatter: (row: RiskControlItem) => (
          <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelete(row.id)}>
            {{
              reference: () => (
                <ElButton type="danger" plain size="small">
                  {$t('common.delete')}
                </ElButton>
              )
            }}
          </ElPopconfirm>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  await removeRiskControlBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
}

async function handleDelete(id: number) {
  await removeRiskControlBatchByIds([id]);
  onDeleted();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <RiskcontrolSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: RiskControlItem[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
            :fixed="col.fixed"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
