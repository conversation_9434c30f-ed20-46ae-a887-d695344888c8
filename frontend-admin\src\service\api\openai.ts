import { request } from '../request';

export function fetchOpenaiPage(params: any) {
  return request({
    url: '/session/page',
    method: 'get',
    params
  });
}

export function saveOpenaiBatch(data: any) {
  return request({
    url: '/session/batch/save',
    data,
    method: 'post'
  });
}

export function updateOpenaiSession(data: any) {
  return request({
    url: '/session/update',
    data,
    method: 'put'
  });
}

export function saveOpenaiSession(data: any) {
  return request({
    url: '/session/create',
    data,
    method: 'post'
  });
}

export function removeOpenaiBatchByIds(data: any) {
  return request({
    url: '/session/delete',
    data,
    method: 'delete'
  });
}

export function fetchOpenaiList() {
  return request({
    url: '/session/list',
    method: 'get'
  });
}

export function getAllOpenaiCarList() {
  return request({
    url: '/session/fetchAllCarList',
    method: 'get'
  });
}

export function updateOpenaiSessionStatus(params: any) {
  return request({
    url: '/session/updateStatus',
    params,
    method: 'put'
  });
}

export const unbindOpenaiSession = (id: string) => {
  return request({
    url: `/session/unbind/${id}`, // 从 params 中获取 id
    method: 'post'
  });
};

export function exportOpenaiData() {
  return request({
    url: '/session/export-sess',
    method: 'get',
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/octet-stream'
    }
  });
}
