<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { fetchCarId, saveClaude, updateCladue } from '@/service/api';

export type OperateType = 'create' | 'edit';

interface FormDataType {
  id: string;
  carID: string;
  email: string;
  isPro: number;
  status: number;
  officialSession: string;
  remark: string;
  createTime: string;
  updateTime: string;
  password?: string;
}

interface Props {
  visible: boolean;
  operateType: OperateType;
  data?: any;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'submitted'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();
const isMobile = computed(() => window.innerWidth < 768);
const dialogWidth = computed(() => (isMobile.value ? '95%' : '50%'));

const formData = ref<FormDataType>({
  id: '',
  carID: '',
  email: '',
  isPro: 1,
  status: 0,
  officialSession: '',
  remark: '',
  createTime: '',
  updateTime: '',
  password: ''
});

const rules = {
  carID: [{ required: true, message: '请输入车号', trigger: 'blur' }],
  email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
  officialSession: [{ required: true, message: '请输入官方session', trigger: 'blur' }]
};

// 监听 props 变化，更新表单数据
watch(
  () => props.visible,
  async newVal => {
    if (newVal && props.operateType === 'edit') {
      // 更新表单数据
      formData.value = {
        ...props.data
      } as FormDataType;
    } else if (props.operateType === 'create') {
      // 清空表单数据，保持默认值
      formData.value = {
        id: '',
        carID: '',
        email: '',
        isPro: 1,
        status: 0,
        officialSession: '',
        remark: '',
        createTime: '',
        updateTime: '',
        password: ''
      };

      try {
        // 异步获取车号
        const response = await fetchCarId();

        // 正确解析后端返回的嵌套结构
        if (response?.response?.data?.msg) {
          formData.value.carID = response.response.data.msg;
        }
      } catch {
        ElMessage.error('获取车号失败');
      }
    }
  },
  { immediate: true }
);

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (props.operateType === 'create') {
          await saveClaude(formData.value);
        } else {
          await updateCladue(formData.value);
        }
        ElMessage.success('保存成功');
        emit('update:visible', false);
        emit('submitted');
      } catch {
        ElMessage.error('保存失败');
      }
    }
  });
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
  formData.value = {
    id: '',
    carID: '',
    email: '',
    isPro: 1,
    status: 0,
    officialSession: '',
    remark: '',
    createTime: '',
    updateTime: '',
    password: ''
  };
};
</script>

<template>
  <ElDialog
    :model-value="visible"
    :title="operateType === 'create' ? '新增' : '编辑'"
    :width="dialogWidth"
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" label-width="auto" :rules="rules">
      <ElRow>
        <ElCol :span="12">
          <ElFormItem label="车号" prop="carID">
            <ElInput v-model="formData.carID" placeholder="请输入车号" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="邮箱" prop="email">
            <ElInput v-model="formData.email" placeholder="请输入邮箱" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow>
        <ElCol :span="12">
          <ElFormItem label="密码" prop="password">
            <ElInput v-model="formData.password" placeholder="请输入密码" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow>
        <ElCol :span="12">
          <ElFormItem label="是否Pro" prop="isPro">
            <ElSwitch v-model="formData.isPro" :active-value="1" :inactive-value="0" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="账号状态" prop="status">
            <ElSwitch v-model="formData.status" :active-value="0" :inactive-value="1" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElFormItem label="官方session" prop="officialSession">
        <ElInput v-model="formData.officialSession" type="textarea" :rows="6" />
      </ElFormItem>
      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="formData.remark" type="textarea" :rows="2" />
      </ElFormItem>
      <div class="m-4 text-right">
        <ElButton @click="handleClose">取 消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
      </div>
    </ElForm>
  </ElDialog>
</template>

<style scoped></style>
