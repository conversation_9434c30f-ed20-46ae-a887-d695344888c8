# 添加模型功能更新说明

## 更新内容

### 1. 简化表单字段
- ❌ 移除了"显示名称"字段
- ✅ 保留"模型名称"字段（作为唯一标识）
- ✅ 新增"限制周期"字段
- ✅ 新增"使用次数"字段

### 2. 表单布局优化
- 采用三列布局：模型名称 | 限制周期 | 使用次数
- 每个字段都有清晰的说明文字
- 支持直接在添加时配置速率限制

### 3. 数据处理逻辑
```javascript
// 新的表单数据结构
const newModelForm = ref({
  name: '',      // 模型名称
  limit: 0,      // 使用次数
  per: ''        // 限制周期
});

// 添加模型时直接设置速率限制
formData.value.modelLimits[modelName] = {
  limit: newModelForm.value.limit || 0,
  per: newModelForm.value.per || ''
};
```

## 使用方法

### 添加新模型
1. 点击"添加模型"按钮
2. 填写表单：
   - **模型名称**：如 `gpt-4o`（必填）
   - **限制周期**：选择时间周期（可选）
   - **使用次数**：输入数字（可选，0表示禁用）
3. 点击"添加"按钮

### 字段说明
- **模型名称**：用于系统识别的唯一标识，同时作为显示名称
- **限制周期**：1s/1m/1h/3h/1d/1w/1y，为空表示无限制
- **使用次数**：在指定周期内的最大使用次数，0表示禁用，为空表示无限制

### 配置示例

#### 示例1：无限制模型
- 模型名称：`gpt-4o`
- 限制周期：（空）
- 使用次数：（空）
- 结果：🟢 无限制使用

#### 示例2：限制使用模型
- 模型名称：`claude-3.5`
- 限制周期：`1h`
- 使用次数：`10`
- 结果：🟡 每小时最多10次

#### 示例3：禁用模型
- 模型名称：`grok-3`
- 限制周期：（任意或空）
- 使用次数：`0`
- 结果：🔴 禁止使用

## 优势

1. **一步到位**：添加模型的同时直接配置速率限制
2. **界面简洁**：移除不必要的显示名称字段
3. **操作高效**：减少后续编辑的步骤
4. **逻辑清晰**：模型名称即显示名称，避免混淆

## 数据结构

最终生成的 `modelLimits` 数据结构：
```json
{
  "gpt-4o": {
    "limit": 10,
    "per": "1h"
  },
  "claude-3.5": {
    "limit": 5,
    "per": "1d"
  },
  "grok-3": {
    "limit": 0,
    "per": ""
  }
}
```

## 兼容性

- 现有的模型配置数据完全兼容
- JSON编辑模式不受影响
- 编辑现有模型的功能保持不变

这个更新让添加模型的流程更加高效，用户可以在一个步骤中完成模型的添加和速率限制配置。
