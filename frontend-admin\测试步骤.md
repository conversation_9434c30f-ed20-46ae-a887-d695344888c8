# 套餐自定义模型管理功能测试步骤

## 测试环境准备
1. 启动开发服务器：`npm run dev`
2. 访问套餐管理页面：`http://localhost:3000/subtype`

## 功能测试清单

### 1. 基础功能测试

#### 1.1 新增套餐测试
- [ ] 点击"新增订阅"按钮
- [ ] 填写基本信息（名称、类型、金额等）
- [ ] 切换到"模型速率限制"标签页
- [ ] 验证界面正常显示

#### 1.2 可视化编辑模式测试
- [ ] 默认显示"可视化编辑"模式
- [ ] 点击"添加模型"按钮
- [ ] 填写模型信息并添加
- [ ] 验证模型卡片正常显示
- [ ] 测试状态标签显示正确

#### 1.3 模型编辑功能测试
- [ ] 点击模型卡片的"编辑"按钮
- [ ] 修改模型名称和显示名称
- [ ] 点击"保存"确认修改
- [ ] 验证修改生效

#### 1.4 速率限制配置测试
- [ ] 设置不同的周期和次数组合
- [ ] 验证状态标签实时更新
- [ ] 测试配置描述正确显示

#### 1.5 模型删除功能测试
- [ ] 点击"删除"按钮
- [ ] 确认删除提示弹窗
- [ ] 验证模型被正确删除

### 2. JSON编辑模式测试

#### 2.1 模式切换测试
- [ ] 切换到"JSON编辑"模式
- [ ] 验证当前数据正确转换为JSON
- [ ] 切换回"可视化编辑"模式
- [ ] 验证数据保持一致

#### 2.2 JSON模板功能测试
- [ ] 点击"使用模板"按钮
- [ ] 验证模板JSON格式正确
- [ ] 点击"应用更改"
- [ ] 验证模板数据正确加载

#### 2.3 JSON编辑功能测试
- [ ] 手动编辑JSON内容
- [ ] 测试正确的JSON格式
- [ ] 测试错误的JSON格式
- [ ] 验证错误提示正确显示

### 3. 数据持久化测试

#### 3.1 保存功能测试
- [ ] 配置多个模型和速率限制
- [ ] 保存套餐
- [ ] 验证数据正确保存

#### 3.2 编辑功能测试
- [ ] 编辑已保存的套餐
- [ ] 验证模型配置正确加载
- [ ] 修改配置并保存
- [ ] 验证修改正确保存

#### 3.3 复制功能测试
- [ ] 复制包含模型配置的套餐
- [ ] 验证模型配置一并复制
- [ ] 修改复制的套餐配置
- [ ] 验证原套餐不受影响

### 4. 界面交互测试

#### 4.1 响应式设计测试
- [ ] 在不同屏幕尺寸下测试
- [ ] 验证布局自适应正常
- [ ] 测试移动端操作体验

#### 4.2 动画效果测试
- [ ] 测试卡片悬停效果
- [ ] 验证状态切换动画
- [ ] 测试模式切换过渡

#### 4.3 用户体验测试
- [ ] 测试操作流程的直观性
- [ ] 验证提示信息的准确性
- [ ] 测试错误处理的友好性

### 5. 边界情况测试

#### 5.1 数据验证测试
- [ ] 测试空模型名称
- [ ] 测试重复模型名称
- [ ] 测试特殊字符输入
- [ ] 测试超长文本输入

#### 5.2 JSON格式测试
- [ ] 测试格式错误的JSON
- [ ] 测试缺少必要字段的JSON
- [ ] 测试包含无效数据的JSON

#### 5.3 性能测试
- [ ] 测试大量模型的处理
- [ ] 测试频繁切换模式的性能
- [ ] 测试长时间操作的稳定性

## 预期结果

### 功能正常工作的标志
1. 所有操作响应迅速，无卡顿
2. 数据保存和加载正确
3. 界面显示美观，交互流畅
4. 错误处理友好，提示准确
5. 不同模式间切换无问题

### 常见问题排查
1. **模型不显示**：检查数据结构是否正确
2. **保存失败**：检查表单验证是否通过
3. **JSON解析错误**：检查JSON格式是否正确
4. **界面异常**：检查CSS样式是否加载

## 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

功能测试结果：
- 基础功能：✅/❌
- JSON编辑：✅/❌  
- 数据持久化：✅/❌
- 界面交互：✅/❌
- 边界情况：✅/❌

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```

完成所有测试项目后，功能即可投入使用。
