<script setup lang="ts">
import { usePermission } from '@/hooks/business/auth';
import { $t } from '@/locales';
const { hasPermission } = usePermission();
defineOptions({ name: 'UserTableHeaderOperation' });

interface Emits {
  (e: 'add'): void;
  (e: 'batchAdd'): void;
  (e: 'delete'): void;
  (e: 'refresh'): void;
  (e: 'compensate'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<UI.TableColumnCheck[]>('columns', {
  default: () => []
});

function add() {
  emit('add');
}

function batchAdd() {
  emit('batchAdd');
}

function compensate() {
  emit('compensate');
}

function batchDelete() {
  emit('delete');
}

function refresh() {
  emit('refresh');
}
</script>

<template>
  <ElSpace direction="horizontal" wrap justify="end" class="lt-sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <ElButton v-show="hasPermission('user/create')" plain type="primary" @click="add">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        新增
      </ElButton>
      <ElTooltip content="批量创建授权码或者用户">
        <ElButton v-show="hasPermission('user/batchAdd')" plain type="success" @click="batchAdd">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          批量创建
        </ElButton>
      </ElTooltip>
      <ElPopconfirm :title="$t('common.confirmDelete')" @confirm="batchDelete">
        <template #reference>
          <ElButton v-show="hasPermission('user/delete')" type="danger" plain>
            <template #icon>
              <icon-ic-round-delete class="text-icon" />
            </template>
            {{ $t('common.batchDelete') }}
          </ElButton>
        </template>
      </ElPopconfirm>
    </slot>
    <ElButton type="warning" plain @click="compensate">时长补偿</ElButton>
    <ElButton @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" />
      </template>
      {{ $t('common.refresh') }}
    </ElButton>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </ElSpace>
</template>

<style scoped></style>
