<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { createUser, fetchGetTenantList, updateUser } from '@/service/api';
import { useForm } from '@/hooks/common/form';

defineOptions({ name: 'UserOperateModal' });

interface Props {
  operateType: 'create' | 'update';
  data: any;
  options: any[];
  modelTypes: { id: string; name: string }[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', { default: false });

const { formRef, validate } = useForm();
const tenantList = ref<Array<{ tenantId: string; tenantName: string }>>([]);

const title = computed(() => (props.operateType === 'create' ? '新增' : '修改'));
const isMobile = computed(() => window.innerWidth < 768);
const activeTab = ref('1');

// 用户类型选项
const userTypes = [
  { value: 1, label: '普通用户' },
  { value: 2, label: '免费用户' },
  { value: 3, label: '付费用户' },
  { value: 4, label: 'Pro用户' }
];

// 限制周期选项
const limits = [
  { value: '1s', label: '每秒' },
  { value: '1m', label: '每分' },
  { value: '1h', label: '每小时' },
  { value: '3h', label: '每3小时' },
  { value: '5h', label: '每5小时' },
  { value: '1d', label: '每天' },
  { value: '1w', label: '每周' },
  { value: '1y', label: '每年' }
];

// 日期快捷选项
const shortcuts = [
  {
    text: '1天后',
    value: () => {
      const date = new Date();
      date.setDate(date.getDate() + 1);
      return date;
    }
  },
  {
    text: '7天后',
    value: () => {
      const date = new Date();
      date.setDate(date.getDate() + 7);
      return date;
    }
  },
  {
    text: '30天后',
    value: () => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date;
    }
  },
  {
    text: '60天后',
    value: () => {
      const date = new Date();
      date.setDate(date.getDate() + 60);
      return date;
    }
  },
  {
    text: '90天后',
    value: () => {
      const date = new Date();
      date.setDate(date.getDate() + 90);
      return date;
    }
  },
  {
    text: '180天后',
    value: () => {
      const date = new Date();
      date.setDate(date.getDate() + 180);
      return date;
    }
  },
  {
    text: '365天后',
    value: () => {
      const date = new Date();
      date.setDate(date.getDate() + 365);
      return date;
    }
  }
];

// 表单验证规则
const rules = reactive({
  userToken: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (/[\u4E00-\u9FA5]/.test(value)) {
          return callback(new Error('用户名不能包含中文字符'));
        }
        return callback();
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  expireTime: [{ required: true, message: '请选择过期时间', trigger: 'blur' }]
});

// 确保模型速率格式正确
const ensureModelLimitsStructure = () => {
  // 如果模型限制对象不存在或不是对象类型，初始化为空对象
  if (!props.data.modelLimits || typeof props.data.modelLimits !== 'object') {
    props.data.modelLimits = {};
  }

  // 遍历模型类型，确保每个模型都有正确的数据结构
  props.modelTypes.forEach(model => {
    if (!props.data.modelLimits[model.id] || typeof props.data.modelLimits[model.id] !== 'object') {
      props.data.modelLimits[model.id] = { limit: 0, per: '' };
    } else {
      // 确保每个模型对象都有limit和per属性
      const modelLimit = props.data.modelLimits[model.id];
      props.data.modelLimits[model.id] = {
        limit: 'limit' in modelLimit ? modelLimit.limit : 0,
        per: 'per' in modelLimit ? modelLimit.per : ''
      };
    }
  });
};

// 保存或更新用户前处理模型速率数据
const prepareModelLimitsData = () => {
  const modelLimits = { ...props.data.modelLimits };

  // 检查并转换模型限制数据
  Object.keys(modelLimits).forEach(modelId => {
    const limit = modelLimits[modelId];
    // 如果per为空且limit为0，这表示禁止使用该模型
    if (!limit.per && limit.limit === 0) {
      modelLimits[modelId] = { limit: 0, per: null };
    }
    // 如果per不为空但limit为0，这是无效状态，设置为禁止使用
    else if (limit.per && limit.limit === 0) {
      modelLimits[modelId] = { limit: 0, per: null };
    }
    // 如果per为空但limit不为0，这表示无限制
    else if (!limit.per && limit.limit > 0) {
      modelLimits[modelId] = { limit: limit.limit, per: null };
    }
  });

  return modelLimits;
};

// 保存或更新用户
async function saveOrUpdate() {
  try {
    await validate();

    // 处理模型速率数据
    props.data.modelLimits = prepareModelLimitsData();

    if (props.operateType === 'create') {
      await createUser(props.data);
      ElMessage({
        message: '新增成功',
        type: 'success'
      });
    } else {
      await updateUser(props.data);
      ElMessage({
        message: '修改成功',
        type: 'success'
      });
    }

    visible.value = false;
    emit('submitted');
  } catch {}
}

// 关闭弹窗
function handleClose() {
  visible.value = false;
}

// 当弹窗显示时，确保模型速率数据结构正确
watch(visible, val => {
  if (val && props.data) {
    ensureModelLimitsStructure();
  }
  fetchGetTenantList().then(res => {
    tenantList.value = (res.data || []) as Array<{ tenantId: string; tenantName: string }>;
    tenantList.value.unshift({ tenantId: '000000', tenantName: '主站' });
  });
});

function handleIdsChange(values: string[]) {
  // Ensure we're working with string values
  props.data.ids = values.map(String);
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :title="title"
    :width="isMobile ? '95%' : '60%'"
    :close-on-click-modal="false"
    align-center
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="data" :rules="rules" :label-position="isMobile ? 'top' : 'right'" label-width="auto">
      <ElTabs v-model="activeTab" type="border-card">
        <ElTabPane label="基本信息" name="1">
          <ElRow>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="用户名" prop="userToken">
                <ElInput v-model="data.userToken" placeholder="请输入用户名" />
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem v-if="operateType === 'create'" label="密码" prop="password">
                <ElInput v-model="data.password" type="password" placeholder="请输入密码" show-password />
              </ElFormItem>
              <ElFormItem v-else label="密码">
                <ElInput v-model="data.password" type="password" placeholder="请输入密码" show-password />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="邮箱" prop="email">
                <ElInput v-model="data.email" placeholder="请输入邮箱" />
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="用户类型" prop="userType">
                <ElSelect v-model="data.userType" placeholder="请选择用户类型">
                  <ElOption v-for="item in userTypes" :key="item.value" :label="item.label" :value="item.value" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="返现比例" prop="affRate">
                <ElInputNumber
                  v-model="data.affRate"
                  :precision="2"
                  :step="0.1"
                  :min="0.1"
                  :max="1"
                  placeholder="请输入返现比例"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="所属分站" prop="tenantName">
                <ElSelect v-model="data.tenantId" placeholder="请选择所属分站" clearable>
                  <ElOption
                    v-for="item in tenantList"
                    :key="item.tenantId"
                    :label="item.tenantName"
                    :value="item.tenantId"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElTabPane>

        <!-- 独享账号设置 -->
        <ElTabPane label="独享账号设置" name="2">
          <ElRow>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="独享账号列表" prop="ids">
                <ElSelect
                  v-model="data.ids"
                  multiple
                  filterable
                  collapse-tags
                  placeholder="请选择车号"
                  clearable
                  tag-type="primary"
                  collapse-tags-tooltip
                  @update:model-value="handleIdsChange"
                >
                  <ElOption v-for="item in props.options" :key="item.id" :label="item.label" :value="String(item.id)" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="独享时间" prop="exclusiveExpireTime">
                <ElDatePicker
                  v-model="data.exclusiveExpireTime"
                  placeholder="请选择日期"
                  type="datetime"
                  :shortcuts="shortcuts"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElTabPane>

        <!-- 模型使用限制 -->
        <ElTabPane label="模型使用限制" name="3">
          <div class="mb-4 text-red-500 font-bold">周期或者次数为空则不限制，次数为0则禁止使用</div>
          <div v-for="model in modelTypes" :key="model.id" class="mb-4">
            <ElRow>
              <ElCol :sm="24" :md="12" :lg="12">
                <ElFormItem :label="`${model.name}速率周期`" :prop="`modelLimits.${model.id}.per`">
                  <ElSelect
                    v-model="data.modelLimits[model.id].per"
                    :placeholder="`请选择${model.name}速率限制周期`"
                    style="width: 100%"
                    clearable
                  >
                    <ElOption v-for="item in limits" :key="item.value" :label="item.label" :value="item.value" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :sm="24" :md="12" :lg="12">
                <ElFormItem :label="`${model.name}使用次数`" :prop="`modelLimits.${model.id}.limit`">
                  <ElInputNumber
                    v-model="data.modelLimits[model.id].limit"
                    style="width: 100%"
                    :placeholder="`请输入${model.name}限制次数`"
                    :min="0"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>
        </ElTabPane>

        <!-- 过期时间设置 -->
        <ElTabPane label="过期时间设置" name="4">
          <ElRow>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="过期时间（普通）" prop="expireTime">
                <ElDatePicker
                  v-model="data.expireTime"
                  placeholder="请选择日期"
                  type="datetime"
                  :shortcuts="shortcuts"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="过期时间（Plus）" prop="plusExpireTime">
                <ElDatePicker
                  v-model="data.plusExpireTime"
                  placeholder="请选择日期"
                  type="datetime"
                  :shortcuts="shortcuts"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="Claude过期时间（普通）" prop="claudeExpireTime">
                <ElDatePicker
                  v-model="data.claudeExpireTime"
                  placeholder="请选择日期"
                  type="datetime"
                  :shortcuts="shortcuts"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="Claude过期时间（pro）" prop="claudeProExpireTime">
                <ElDatePicker
                  v-model="data.claudeProExpireTime"
                  placeholder="请选择日期"
                  type="datetime"
                  :shortcuts="shortcuts"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="Grok过期时间" prop="grokExpireTime">
                <ElDatePicker
                  v-model="data.grokExpireTime"
                  placeholder="请选择日期"
                  type="datetime"
                  :shortcuts="shortcuts"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :sm="24" :md="12" :lg="12">
              <ElFormItem label="Grok Super过期时间" prop="grokSuperExpireTime">
                <ElDatePicker
                  v-model="data.grokSuperExpireTime"
                  placeholder="请选择日期"
                  type="datetime"
                  :shortcuts="shortcuts"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElTabPane>

        <!-- 备注信息 -->
        <ElTabPane label="备注信息" name="5">
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="data.remark" type="textarea" :rows="4" placeholder="请输入备注" />
          </ElFormItem>
        </ElTabPane>
      </ElTabs>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取 消</ElButton>
        <ElButton type="primary" @click="saveOrUpdate">保 存</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<style scoped>
:deep(.el-tabs__content) {
  padding: 20px;
}
</style>
