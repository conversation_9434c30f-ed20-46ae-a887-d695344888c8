<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { CheckboxValueType } from 'element-plus';
import { addSubType, fetchGetTenantList, updateSubType } from '@/service/api';

export type OperateType = 'create' | 'edit' | 'copy';

// 定义类型接口
interface ModelLimit {
  limit: number;
  per: string;
}

interface ModelLimits {
  [key: string]: ModelLimit;
}

// 自定义模型接口
interface CustomModel {
  id: string;
  name: string;
  displayName: string;
  editing?: boolean;
}

interface FormDataType {
  id: string | number;
  name: string;
  money: string | number;
  validDays: number;
  isPlus: number;
  sort: number;
  updateTime: string;
  createTime: string;
  features: string[];
  modelLimits: ModelLimits;
  subType: string;
  exclusive: number;
  exclusiveType: number;
  isHotSale: number;
  isPro: number;
  isSuper: number;
  isNotValued: number;
  [key: string]: any;
}

interface Props {
  visible: boolean;
  operateType: OperateType;
  data?: any;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'submitted'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const tenantList = ref<Array<{ tenantId: string; tenantName: string }>>([]);

const formRef = ref();
const activeNames = ref('1');
const isMobile = computed(() => window.innerWidth < 768);
const dialogWidth = computed(() => (isMobile.value ? '95%' : '50%'));

// 自定义模型列表
const customModels = ref<CustomModel[]>([]);

// 新增模型表单
const newModelForm = ref({
  name: '',
  limit: 0,
  per: ''
});

// 编辑模型表单
const editModelForm = ref({
  id: '',
  name: '',
  displayName: ''
});

// 控制状态
const showAddModelForm = ref(false);
const editMode = ref<'visual' | 'json'>('visual');
const jsonEditorValue = ref('');
const jsonError = ref('');

// 速率限制选项
const limits = [
  { value: '1s', label: '每秒' },
  { value: '1m', label: '每分' },
  { value: '1h', label: '每小时' },
  { value: '3h', label: '每3小时' },
  { value: '1d', label: '每天' },
  { value: '1w', label: '每周' },
  { value: '1y', label: '每年' }
];

// 订阅类型选项
const subTypes = [
  { value: '1', label: 'ChatGPT套餐' },
  { value: '2', label: 'Claude套餐' },
  { value: '3', label: 'ChatGPT&Claude套餐' },
  { value: '4', label: 'Grok套餐' },
  { value: '5', label: 'ChatGPT&Grok套餐' },
  { value: '6', label: 'Claude&Grok套餐' },
  { value: '7', label: 'Chatgpt&Claude&Grok套餐' },
  { value: '8', label: '绘画套餐' }
];

// 功能标签选项
const options = [
  { value: '官方同款功能和UI，一比一还原体验。', label: '官方同款功能和UI，一比一还原体验。' },
  { value: '降低使用门槛：支持免梯直登，无需任何魔法', label: '降低使用门槛：支持免梯直登，无需任何魔法' },
  { value: '支持对话隔离，保护用户隐私。', label: '支持对话隔离，保护用户隐私。' },
  {
    value: '支持插件功能：语音、联网、绘画、识图、文档解析、深度思考。',
    label: '支持插件功能：语音、联网、绘画、识图、文档解析、深度思考。'
  },
  { value: '支持GPT全模型：官网有的模型，我们都有。', label: '支持GPT全模型：官网有的模型，我们都有。' },
  {
    value: '内置本地Deepseek R1满血版，免受官网【服务器繁忙】的困扰。',
    label: '内置本地Deepseek R1满血版，免受官网【服务器繁忙】的困扰。'
  },
  { value: '支持Grok全模型，响应最快，主打丝滑。', label: '支持Grok全模型，响应最快，主打丝滑。' },
  { value: '支持Claude3.7，超长上下文，理工科必备生产力。', label: '支持Claude3.7，超长上下文，理工科必备生产力。' },
  { value: '专属客服解答：提供售前咨询，售后保障。', label: '专属客服解答：提供售前咨询，售后保障。' },
  {
    value: '智能AI绘图：文生图功能，将文字描述转化为精美图像。',
    label: '智能AI绘图：文生图功能，将文字描述转化为精美图像。'
  },
  {
    value: '智能图像编辑：上传图片进行风格化改造和智能编辑。',
    label: '智能图像编辑：上传图片进行风格化改造和智能编辑。'
  },
  {
    value: '多种绘图风格选择：写实、动漫、油画、素描等多种风格可选。',
    label: '多种绘图风格选择：写实、动漫、油画、素描等多种风格可选。'
  },
  {
    value: '高清图像生成：支持生成高分辨率精美图像。',
    label: '高清图像生成：支持生成高分辨率精美图像。'
  },
  {
    value: '图像放大与优化：将模糊或低分辨率图像转换为高清晰度版本。',
    label: '图像放大与优化：将模糊或低分辨率图像转换为高清晰度版本。'
  },
  {
    value: '专业提示词指导：内置提示词模板，让AI绘图更加精准。',
    label: '专业提示词指导：内置提示词模板，让AI绘图更加精准。'
  }
];

const formData = ref<FormDataType>({
  id: '',
  name: '',
  money: '',
  validDays: 0,
  isPlus: 0,
  sort: 1,
  updateTime: '',
  createTime: '',
  features: [],
  modelLimits: {},
  subType: '',
  exclusive: 0,
  exclusiveType: 0,
  isHotSale: 0,
  isPro: 0,
  isSuper: 0,
  isNotValued: 0
});

const rules = {
  name: [{ required: true, message: '请输入订阅名称', trigger: 'blur' }],
  validDays: [{ required: true, message: '请输入有效天数', trigger: 'blur' }],
  money: [{ required: true, message: '请输入订阅金额', trigger: 'blur' }],
  subType: [{ required: true, message: '请选择订阅类型', trigger: 'blur' }]
};

// 添加新模型
const addNewModel = () => {
  if (!newModelForm.value.name.trim()) {
    ElMessage.warning('请填写模型名称');
    return;
  }

  // 检查模型名称是否已存在
  if (customModels.value.some(model => model.name === newModelForm.value.name)) {
    ElMessage.warning('模型名称已存在');
    return;
  }

  const modelName = newModelForm.value.name.trim();

  const newModel: CustomModel = {
    id: Date.now().toString(),
    name: modelName,
    displayName: modelName, // 显示名称使用模型名称
    editing: false
  };

  customModels.value.push(newModel);

  // 直接设置用户填写的速率限制
  if (!formData.value.modelLimits) {
    formData.value.modelLimits = {};
  }
  formData.value.modelLimits[modelName] = {
    limit: newModelForm.value.limit || 0,
    per: newModelForm.value.per || ''
  };

  // 重置表单
  newModelForm.value = {
    name: '',
    limit: 0,
    per: ''
  };
  showAddModelForm.value = false;

  ElMessage.success('模型添加成功');
};

// 开始编辑模型
const startEditModel = (model: CustomModel) => {
  // 先取消其他模型的编辑状态
  customModels.value.forEach(m => (m.editing = false));

  model.editing = true;
  editModelForm.value = {
    id: model.id,
    name: model.name,
    displayName: model.displayName
  };
};

// 保存模型编辑
const saveModelEdit = (model: CustomModel) => {
  if (!editModelForm.value.name.trim() || !editModelForm.value.displayName.trim()) {
    ElMessage.warning('请填写模型名称和显示名称');
    return;
  }

  // 检查模型名称是否与其他模型重复
  if (customModels.value.some(m => m.id !== model.id && m.name === editModelForm.value.name)) {
    ElMessage.warning('模型名称已存在');
    return;
  }

  // 如果模型名称发生变化，需要更新modelLimits中的键
  if (model.name !== editModelForm.value.name) {
    const oldName = model.name;
    const newName = editModelForm.value.name.trim();

    if (formData.value.modelLimits[oldName]) {
      formData.value.modelLimits[newName] = formData.value.modelLimits[oldName];
      const newLimits = { ...formData.value.modelLimits };
      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete newLimits[oldName];
      formData.value.modelLimits = newLimits;
    }
  }

  // 更新模型信息
  model.name = editModelForm.value.name.trim();
  model.displayName = editModelForm.value.displayName.trim();
  model.editing = false;

  ElMessage.success('模型更新成功');
};

// 取消编辑模型
const cancelModelEdit = (model: CustomModel) => {
  model.editing = false;
};

// 删除模型
const removeModel = async (model: CustomModel) => {
  try {
    await ElMessageBox.confirm(`确定要删除模型 "${model.displayName}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 从列表中移除
    const index = customModels.value.findIndex(m => m.id === model.id);
    if (index > -1) {
      customModels.value.splice(index, 1);
    }

    // 删除对应的速率限制配置
    if (formData.value.modelLimits && formData.value.modelLimits[model.name]) {
      const newLimits = { ...formData.value.modelLimits };
      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete newLimits[model.name];
      formData.value.modelLimits = newLimits;
    }

    ElMessage.success('模型删除成功');
  } catch {
    // 用户取消删除
  }
};

// 切换编辑模式
const switchEditMode = (mode: 'visual' | 'json') => {
  if (mode === 'json') {
    // 切换到JSON模式时，将当前数据转换为JSON
    syncToJson();
  } else {
    // 切换到可视化模式时，尝试解析JSON
    if (jsonEditorValue.value.trim()) {
      parseJsonToVisual();
    }
  }
  editMode.value = mode;
};

// 同步数据到JSON编辑器
const syncToJson = () => {
  try {
    // 直接编辑 modelLimits 字段
    jsonEditorValue.value = JSON.stringify(formData.value.modelLimits || {}, null, 2);
    jsonError.value = '';
  } catch (error) {
    jsonError.value = '数据序列化失败';
  }
};

// 解析JSON到可视化模式
const parseJsonToVisual = () => {
  try {
    const modelLimits = JSON.parse(jsonEditorValue.value);

    // 直接更新 modelLimits
    formData.value.modelLimits = modelLimits;

    // 根据 modelLimits 的键重建模型列表用于显示
    customModels.value = Object.keys(modelLimits).map((modelName, index) => ({
      id: Date.now().toString() + index,
      name: modelName,
      displayName: modelName, // 显示名称默认使用模型名称
      editing: false
    }));

    jsonError.value = '';
    ElMessage.success('JSON解析成功');
  } catch (error) {
    jsonError.value = `JSON解析失败: ${error instanceof Error ? error.message : '未知错误'}`;
    ElMessage.error(jsonError.value);
  }
};

// 应用JSON更改
const applyJsonChanges = () => {
  parseJsonToVisual();
};

// 获取JSON模板
const getJsonTemplate = () => {
  const template = {
    'gpt-4o': {
      limit: 10,
      per: '1h'
    },
    'claude-3.5': {
      limit: 5,
      per: '1d'
    },
    'grok-3': {
      limit: 0,
      per: ''
    }
  };

  jsonEditorValue.value = JSON.stringify(template, null, 2);
  jsonError.value = '';
};

// 处理订阅类型变更
const handleSubTypeChange = () => {
  // 清空独享设置
  formData.value.exclusive = 0;
};

// 获取限制描述
const getLimitDescription = (limit: ModelLimit) => {
  if (!limit.per || !limit.limit) return '配置不完整';

  const periodMap: Record<string, string> = {
    '1s': '每秒',
    '1m': '每分钟',
    '1h': '每小时',
    '3h': '每3小时',
    '1d': '每天',
    '1w': '每周',
    '1y': '每年'
  };

  const periodText = periodMap[limit.per] || limit.per;
  return `${periodText}最多${limit.limit}次`;
};

// 处理全选功能标签
const handleCheckAll = (val: CheckboxValueType) => {
  formData.value.features = val ? options.map(item => item.value) : [];
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (props.operateType === 'create' || props.operateType === 'copy') {
          await addSubType(formData.value);
        } else {
          await updateSubType(formData.value);
        }
        ElMessage.success('保存成功');
        emit('update:visible', false);
        emit('submitted');
      } catch {
        ElMessage.error('保存失败');
      }
    }
  });
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();

  // 重置表单数据
  formData.value = {
    id: '',
    name: '',
    money: '',
    validDays: 0,
    isPlus: 0,
    sort: 1,
    updateTime: '',
    createTime: '',
    features: [],
    modelLimits: {},
    subType: '',
    exclusive: 0,
    exclusiveType: 0,
    isHotSale: 0,
    isPro: 0,
    isSuper: 0,
    isNotValued: 0
  } as FormDataType;

  // 重置自定义模型相关状态
  customModels.value = [];
  newModelForm.value = {
    name: '',
    limit: 0,
    per: ''
  };
  editModelForm.value = {
    id: '',
    name: '',
    displayName: ''
  };
  showAddModelForm.value = false;
  editMode.value = 'visual';
  jsonEditorValue.value = '';
  jsonError.value = '';
};

// 监听 props 变化，更新表单数据
watch(
  () => props.data,
  newVal => {
    if (newVal && (props.operateType === 'edit' || props.operateType === 'copy')) {
      // 从现有数据中恢复自定义模型列表
      const modelLimits = newVal.modelLimits || {};

      // 根据 modelLimits 重建自定义模型列表
      customModels.value = Object.keys(modelLimits).map(modelName => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: modelName,
        displayName: modelName, // 如果没有存储显示名称，使用模型名称作为显示名称
        editing: false
      }));

      // 直接使用现有的 modelLimits
      const processedModelLimits: ModelLimits = { ...modelLimits };

      // 更新表单数据
      const baseData = {
        ...newVal,
        modelLimits: processedModelLimits,
        features: newVal.features || []
      } as FormDataType;

      // 如果是复制模式，清除ID并修改名称
      if (props.operateType === 'copy') {
        baseData.id = '';
        baseData.name = `${newVal.name} - 副本`;
        baseData.createTime = '';
        baseData.updateTime = '';
      }

      formData.value = baseData;
    }
    fetchGetTenantList().then(res => {
      tenantList.value = (res.data || []) as Array<{ tenantId: string; tenantName: string }>;
      tenantList.value.unshift({ tenantId: '000000', tenantName: '主站' });
    });
  },
  { immediate: true }
);
</script>

<template>
  <ElDialog
    :model-value="visible"
    :title="operateType === 'create' ? '新增订阅' : operateType === 'copy' ? '复制订阅' : '编辑订阅'"
    :width="dialogWidth"
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" label-width="auto" :rules="rules">
      <ElTabs v-model="activeNames" type="border-card">
        <ElTabPane label="基本信息" name="1">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="订阅名称" prop="name">
                <ElInput v-model="formData.name" placeholder="请输入订阅名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="订阅类型" prop="subType">
                <ElSelect v-model="formData.subType" placeholder="请选择订阅类型" @change="handleSubTypeChange">
                  <ElOption v-for="item in subTypes" :key="item.value" :label="item.label" :value="item.value" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="所属分站" prop="tenantId">
                <ElSelect v-model="formData.tenantId" placeholder="请选择所属分站（可选）" clearable>
                  <ElOption
                    v-for="item in tenantList"
                    :key="item.tenantId"
                    :label="item.tenantName"
                    :value="item.tenantId"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="订阅金额" prop="money">
                <ElInput v-model="formData.money" placeholder="请输入订阅金额" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="有效天数" prop="validDays">
                <ElInputNumber v-model="formData.validDays" placeholder="请输入有效天数" />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="formData.subType === '8'" :span="12">
              <ElFormItem label="绘画额度" prop="drawQuota">
                <ElInputNumber v-model="formData.drawQuota" placeholder="请输入绘画额度" />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElFormItem v-if="formData.subType === '1'" label="是否独享" prop="exclusive">
            <ElSwitch
              v-model="formData.exclusive"
              inline-prompt
              active-text="独享"
              inactive-text="共享"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem v-if="formData.exclusive === 1" label="独享类型" prop="exclusiveType">
            <ElRadioGroup v-model="formData.exclusiveType">
              <ElRadio :value="0" size="large">Free</ElRadio>
              <ElRadio :value="1" size="large">Plus</ElRadio>
              <ElRadio :value="2" size="large">Team</ElRadio>
              <ElRadio :value="3" size="large">Pro</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="显示热卖标志" prop="isHotSale">
            <ElSwitch v-model="formData.isHotSale" :active-value="1" :inactive-value="0" />
          </ElFormItem>
          <ElFormItem v-if="['1', '3', '5', '7'].includes(formData.subType)" label="Plus权限" prop="isPlus">
            <ElSwitch
              v-model="formData.isPlus"
              inline-prompt
              active-text="可用ChatGPT Plus"
              inactive-text="可用ChatGPT 普号"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem v-if="['2', '3', '6', '7'].includes(formData.subType)" label="Pro权限" prop="isPro">
            <ElSwitch
              v-model="formData.isPro"
              inline-prompt
              active-text="可用Claude Pro"
              inactive-text="可用Claude 普号"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem v-if="['4', '5', '6', '7'].includes(formData.subType)" label="Super权限" prop="isSuper">
            <ElSwitch
              v-model="formData.isSuper"
              inline-prompt
              active-text="可用Grok super"
              inactive-text="可用Grok 普号"
              :active-value="1"
              :inactive-value="0"
            />
          </ElFormItem>
          <ElFormItem label="前台展示" prop="isNotValued">
            <ElSwitch v-model="formData.isNotValued" :active-value="0" :inactive-value="1" />
          </ElFormItem>
        </ElTabPane>

        <ElTabPane v-if="formData.subType !== '8'" label="模型速率限制" name="2">
          <div class="space-y-6">
            <!-- 编辑模式切换 -->
            <ElCard shadow="never" class="border-2 border-blue-200">
              <div class="mb-4 flex items-center justify-between">
                <div class="text-lg text-gray-800 font-medium">编辑模式</div>
                <ElRadioGroup v-model="editMode" @change="switchEditMode">
                  <ElRadioButton value="visual">可视化编辑</ElRadioButton>
                  <ElRadioButton value="json">JSON编辑</ElRadioButton>
                </ElRadioGroup>
              </div>

              <!-- JSON编辑模式 -->
              <div v-if="editMode === 'json'" class="space-y-4">
                <div class="flex items-center justify-between">
                  <div class="text-sm text-gray-600">使用JSON格式批量配置模型和速率限制</div>
                  <div class="space-x-2">
                    <ElButton size="small" @click="getJsonTemplate">使用模板</ElButton>
                    <ElButton type="primary" size="small" @click="applyJsonChanges">应用更改</ElButton>
                  </div>
                </div>

                <ElInput
                  v-model="jsonEditorValue"
                  type="textarea"
                  :rows="15"
                  placeholder="请输入JSON配置..."
                  class="font-mono"
                />

                <div v-if="jsonError" class="text-sm text-red-500">
                  {{ jsonError }}
                </div>

                <ElAlert title="JSON格式说明" type="info" :closable="false">
                  <template #default>
                    <div class="text-sm space-y-2">
                      <div><strong>直接编辑 modelLimits 对象：</strong></div>
                      <div class="rounded bg-gray-100 p-2 text-xs font-mono">
                        {
                        <br />
                        &nbsp;&nbsp;"模型名称": {
                        <br />
                        &nbsp;&nbsp;&nbsp;&nbsp;"limit": 使用次数,
                        <br />
                        &nbsp;&nbsp;&nbsp;&nbsp;"per": "时间周期"
                        <br />
                        &nbsp;&nbsp;}
                        <br />
                        }
                      </div>
                      <div>
                        <strong>时间周期</strong>
                        : 1s/1m/1h/3h/1d/1w/1y
                      </div>
                      <div>
                        <strong>使用次数</strong>
                        : 0表示禁用，空值表示无限制
                      </div>
                    </div>
                  </template>
                </ElAlert>
              </div>
            </ElCard>

            <!-- 可视化编辑模式 -->
            <div v-if="editMode === 'visual'" class="space-y-4">
              <!-- 添加模型区域 -->
              <ElCard
                shadow="never"
                class="border-2 border-gray-300 border-dashed transition-colors hover:border-blue-400"
              >
                <div v-if="!showAddModelForm" class="py-6 text-center">
                  <ElButton type="primary" size="large" @click="showAddModelForm = true">
                    <ElIcon class="mr-2"><Plus /></ElIcon>
                    添加模型
                  </ElButton>
                  <div class="mt-2 text-sm text-gray-500">点击添加自定义模型</div>
                </div>

                <div v-else class="space-y-4">
                  <div class="flex items-center justify-between">
                    <div class="text-lg text-gray-800 font-medium">添加新模型</div>
                    <ElButton size="small" @click="showAddModelForm = false">取消</ElButton>
                  </div>
                  <ElRow :gutter="16">
                    <ElCol :span="8">
                      <ElFormItem label="模型名称" required>
                        <ElInput v-model="newModelForm.name" placeholder="如：gpt-4o" @keyup.enter="addNewModel" />
                        <div class="mt-1 text-xs text-gray-500">用于系统识别的唯一标识</div>
                      </ElFormItem>
                    </ElCol>
                    <ElCol :span="8">
                      <ElFormItem label="限制周期">
                        <ElSelect v-model="newModelForm.per" placeholder="选择周期（可选）" clearable>
                          <ElOption v-for="item in limits" :key="item.value" :label="item.label" :value="item.value" />
                        </ElSelect>
                        <div class="mt-1 text-xs text-gray-500">时间周期</div>
                      </ElFormItem>
                    </ElCol>
                    <ElCol :span="8">
                      <ElFormItem label="使用次数">
                        <ElInputNumber
                          v-model="newModelForm.limit"
                          :min="0"
                          placeholder="次数（可选）"
                          class="w-full"
                        />
                        <div class="mt-1 text-xs text-gray-500">0表示禁用</div>
                      </ElFormItem>
                    </ElCol>
                  </ElRow>
                  <div class="flex justify-end space-x-2">
                    <ElButton @click="showAddModelForm = false">取消</ElButton>
                    <ElButton type="primary" @click="addNewModel">添加</ElButton>
                  </div>
                </div>
              </ElCard>

              <!-- 配置说明 -->
              <ElAlert title="配置说明" type="info" :closable="false">
                <template #default>
                  <ul class="list-disc list-inside text-sm space-y-1">
                    <li>
                      <strong>周期或次数为空</strong>
                      ：表示不限制该模型的使用
                    </li>
                    <li>
                      <strong>次数为0</strong>
                      ：表示禁止使用该模型
                    </li>
                    <li>
                      <strong>同时设置周期和次数</strong>
                      ：表示在指定周期内最多使用指定次数
                    </li>
                  </ul>
                </template>
              </ElAlert>

              <!-- 模型列表 -->
              <div v-if="customModels.length === 0" class="py-12 text-center">
                <ElEmpty description="暂无模型，请先添加模型" />
              </div>

              <div v-else class="space-y-4">
                <ElCard
                  v-for="model in customModels"
                  :key="model.id"
                  shadow="hover"
                  class="model-limit-card transition-all duration-300"
                >
                  <template #header>
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <!-- 编辑模式 -->
                        <div v-if="model.editing" class="flex items-center space-x-2">
                          <ElInput
                            v-model="editModelForm.displayName"
                            size="small"
                            placeholder="显示名称"
                            class="w-30"
                          />
                          <ElInput v-model="editModelForm.name" size="small" placeholder="模型名称" class="w-30" />
                        </div>
                        <!-- 显示模式 -->
                        <div v-else class="flex items-center space-x-2">
                          <ElTag type="primary" size="large">{{ model.displayName }}</ElTag>
                          <span class="text-sm text-gray-500">{{ model.name }}</span>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <!-- 状态标签 -->
                        <ElTag
                          v-if="!formData.modelLimits[model.name]?.per && !formData.modelLimits[model.name]?.limit"
                          type="success"
                          size="small"
                        >
                          无限制
                        </ElTag>
                        <ElTag v-else-if="formData.modelLimits[model.name]?.limit === 0" type="danger" size="small">
                          禁用
                        </ElTag>
                        <ElTag
                          v-else-if="formData.modelLimits[model.name]?.per && formData.modelLimits[model.name]?.limit"
                          type="warning"
                          size="small"
                        >
                          限制使用
                        </ElTag>

                        <!-- 操作按钮 -->
                        <div v-if="model.editing" class="space-x-1">
                          <ElButton type="primary" size="small" @click="saveModelEdit(model)">保存</ElButton>
                          <ElButton size="small" @click="cancelModelEdit(model)">取消</ElButton>
                        </div>
                        <div v-else class="space-x-1">
                          <ElButton type="primary" size="small" link @click="startEditModel(model)">编辑</ElButton>
                          <ElButton type="danger" size="small" link @click="removeModel(model)">删除</ElButton>
                        </div>
                      </div>
                    </div>
                  </template>

                  <ElRow :gutter="16">
                    <ElCol :span="12">
                      <ElFormItem :prop="`modelLimits.${model.name}.per`" label="限制周期">
                        <ElSelect
                          v-model="formData.modelLimits[model.name].per"
                          placeholder="选择限制周期（可选）"
                          clearable
                        >
                          <ElOption v-for="item in limits" :key="item.value" :label="item.label" :value="item.value" />
                        </ElSelect>
                      </ElFormItem>
                    </ElCol>
                    <ElCol :span="12">
                      <ElFormItem :prop="`modelLimits.${model.name}.limit`" label="使用次数">
                        <ElInputNumber
                          v-model="formData.modelLimits[model.name].limit"
                          :min="0"
                          placeholder="输入使用次数（可选）"
                          class="w-full"
                        />
                      </ElFormItem>
                    </ElCol>
                  </ElRow>

                  <div class="mt-4 rounded-lg from-gray-50 to-blue-50 bg-gradient-to-r p-3 text-sm">
                    <span class="text-gray-600 font-medium">当前配置：</span>
                    <span
                      v-if="!formData.modelLimits[model.name]?.per && !formData.modelLimits[model.name]?.limit"
                      class="ml-2 text-green-600 font-medium"
                    >
                      🟢 无限制使用
                    </span>
                    <span
                      v-else-if="formData.modelLimits[model.name]?.limit === 0"
                      class="ml-2 text-red-600 font-medium"
                    >
                      🔴 禁止使用
                    </span>
                    <span
                      v-else-if="formData.modelLimits[model.name]?.per && formData.modelLimits[model.name]?.limit"
                      class="ml-2 text-orange-600 font-medium"
                    >
                      🟡 {{ getLimitDescription(formData.modelLimits[model.name]) }}
                    </span>
                    <span v-else class="ml-2 text-blue-600 font-medium">🔵 配置不完整，将按无限制处理</span>
                  </div>
                </ElCard>
              </div>
            </div>
          </div>
        </ElTabPane>

        <ElTabPane label="其他信息" name="3">
          <ElFormItem label="商品标签" prop="features">
            <ElCheckbox
              v-if="formData.features"
              :model-value="formData.features.length === options.length"
              :indeterminate="formData.features.length > 0 && formData.features.length < options.length"
              @change="handleCheckAll"
            >
              全选
            </ElCheckbox>
            <ElSelect
              v-model="formData.features"
              multiple
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="选择或者手动输入商品标签"
              clearable
            >
              <ElOption v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="排序" prop="sort">
            <ElInputNumber v-model="formData.sort" :min="1" :max="9999" />
          </ElFormItem>
        </ElTabPane>
      </ElTabs>

      <div class="m-4 text-right">
        <ElButton @click="handleClose">取 消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
      </div>
    </ElForm>
  </ElDialog>
</template>

<style scoped>
.model-limit-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.model-limit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-x-1 > * + * {
  margin-left: 0.25rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.list-disc {
  list-style-type: disc;
}

.list-inside {
  list-style-position: inside;
}

.w-30 {
  width: 120px;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-gray-50 {
  --tw-gradient-from: #f9fafb;
  --tw-gradient-to: rgb(249 250 251 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-blue-50 {
  --tw-gradient-to: #eff6ff;
}

.border-dashed {
  border-style: dashed;
}

.hover\:border-blue-400:hover {
  border-color: #60a5fa;
}
</style>
