<script setup lang="tsx">
import { ref } from 'vue';
import { ElButton, ElMessage, ElPopconfirm, ElSwitch, ElTag } from 'element-plus';
import type { TableColumnCtx } from 'element-plus';
import { useBoolean } from '@sa/hooks';
import { fetchClaudePage, removeClaudeBatchByIds, updateCladue } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import SubtypeSearch from './modules/claude-search.vue';
import ClaudeTableHeaderOperation from './modules/claude-table-header-operation.vue';
import ClaudeOperateModel, { type OperateType } from './modules/claude-operate-modal.vue';

// Define the proper type for your table data
interface Claude {
  id: string;
  carID: string;
  email: string;
  isPro: number;
  status: number;
  officialSession: string;
  remark: string;
  createTime: string;
  updateTime: string;
  password?: string;
  userToken?: string;
  exclusiveExpireTime?: string;
  count?: number;
}
const sortParams = ref({
  prop: '',
  order: ''
});

// Function to convert Element Plus sort order to API sort order
function convertSortOrder(order: string): string | undefined {
  if (order === 'ascending') return 'asc';
  if (order === 'descending') return 'desc';
  return undefined;
}

const { columns, columnChecks, data, loading, pagination, getData, searchParams, resetSearchParams, getDataByPage } =
  useTable<UI.TableApiFn<Claude>>({
    apiFn: params => {
      // 添加排序参数
      const requestParams = {
        ...params,
        sortProp: sortParams.value.prop,
        sortOrder: convertSortOrder(sortParams.value.order)
      };
      return fetchClaudePage(requestParams);
    },
    columns: () => [
      { type: 'selection', width: 48 },
      { type: 'index', label: '序号', align: 'center', width: 60 },
      { prop: 'carID', label: '车号', align: 'center', minWidth: 100 },
      { prop: 'email', label: '邮箱', align: 'center', minWidth: 300, showOverflowTooltip: true },
      {
        prop: 'isPro',
        label: '账号类型',
        align: 'center',
        sortable: true,
        width: 100,
        formatter: ((row: any) => {
          if (row.isPro === 0) return <ElTag type="primary">Sonnet</ElTag>;
          if (row.isPro === 1) return <ElTag type="success">Pro</ElTag>;
          return <ElTag type="info">Unknown</ElTag>;
        }) as TableFormatter
      },
      {
        prop: 'status',
        label: '账号状态',
        align: 'center',
        sortable: true,
        width: 100,
        formatter: (row: Claude) => (
          <ElSwitch v-model={row.status} active-value={0} inactive-value={1} onChange={() => changeSwitch(row)} />
        )
      },
      { prop: 'count', label: '日请求量', align: 'center', width: 100 },
      { prop: 'officialSession', label: '官方session', align: 'center', width: 300, showOverflowTooltip: true },
      { prop: 'remark', label: '备注', align: 'center', width: 200, showOverflowTooltip: true },
      { prop: 'createTime', label: '创建时间', align: 'center', sortable: true, width: 180 },
      { prop: 'updateTime', label: '更新时间', align: 'center', sortable: true, width: 180 },
      {
        prop: 'operate',
        label: '操作',
        align: 'center',
        fixed: 'right',
        width: 140,
        formatter: (row: any) => (
          <div class="flex-center">
            <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
              {$t('common.edit')}
            </ElButton>
            <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelClick(row.id)}>
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {$t('common.delete')}
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data as any, getData);
type TableFormatter = (row: any, column: TableColumnCtx<any>, cellValue: any, index: number) => any;

const wrapperRef = ref<HTMLElement | null>(null);
const operateType = ref<OperateType>('create');
const { bool: visible, setTrue: openModal } = useBoolean();
const currentData = ref<any>(null);

async function changeSwitch(row: Claude) {
  try {
    await updateCladue(row);
    ElMessage.success('更新成功');
    getData();
  } catch {
    ElMessage.error('更新失败');
  }
}

function handleEdit(row: Claude) {
  operateType.value = 'edit';
  currentData.value = data.value.find(item => item.id === row.id);
  openModal();
}

async function handleDelClick(id: string) {
  await removeClaudeBatchByIds([id]);
  onDeleted();
}

// 添加处理排序变化的函数
function handleSortChange({ prop, order }: { prop: string; order: string }) {
  sortParams.value.prop = prop;
  sortParams.value.order = order;
  getData();
}

async function handleBatchDelete() {
  await removeClaudeBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
}

function handleAdd() {
  operateType.value = 'create';
  currentData.value = null;
  openModal();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <SubtypeSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <ClaudeTableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Claude[]) => checkedRowKeys = selection.map(item => item.id)"
          @sort-change="handleSortChange"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <ClaudeOperateModel
        v-model:visible="visible"
        :operate-type="operateType"
        :data="currentData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
