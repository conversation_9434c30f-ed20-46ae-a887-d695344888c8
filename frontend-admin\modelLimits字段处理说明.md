# modelLimits 字段处理说明

## 核心目标

无论是可视化编辑还是JSON编辑，最终都是为了正确处理和生成 `modelLimits` 字段。

## modelLimits 数据结构

```typescript
interface ModelLimits {
  [modelName: string]: {
    limit: number;  // 使用次数限制
    per: string;    // 时间周期
  }
}
```

## 示例数据

```json
{
  "gpt-4o": {
    "limit": 10,
    "per": "1h"
  },
  "claude-3.5": {
    "limit": 5,
    "per": "1d"
  },
  "grok-3": {
    "limit": 0,
    "per": ""
  }
}
```

## 配置规则

1. **无限制使用**：`limit` 为空或 `per` 为空
2. **禁止使用**：`limit` 为 0
3. **限制使用**：同时设置 `limit` 和 `per`

## 时间周期选项

- `1s` - 每秒
- `1m` - 每分钟
- `1h` - 每小时
- `3h` - 每3小时
- `1d` - 每天
- `1w` - 每周
- `1y` - 每年

## 可视化编辑模式

### 功能
- 添加模型：自动在 `modelLimits` 中创建对应条目
- 编辑模型：修改模型名称时更新 `modelLimits` 的键
- 删除模型：从 `modelLimits` 中移除对应条目
- 配置限制：直接修改 `modelLimits` 中的 `limit` 和 `per` 值

### 核心逻辑
```javascript
// 添加模型时
formData.value.modelLimits[newModel.name] = {
  limit: 0,
  per: ''
};

// 删除模型时
delete formData.value.modelLimits[model.name];

// 修改模型名称时
formData.value.modelLimits[newName] = formData.value.modelLimits[oldName];
delete formData.value.modelLimits[oldName];
```

## JSON编辑模式

### 功能
- 直接编辑 `modelLimits` 对象
- 支持批量配置
- 实时验证JSON格式

### 核心逻辑
```javascript
// 同步到JSON编辑器
const syncToJson = () => {
  jsonEditorValue.value = JSON.stringify(formData.value.modelLimits || {}, null, 2);
};

// 从JSON解析
const parseJsonToVisual = () => {
  const modelLimits = JSON.parse(jsonEditorValue.value);
  formData.value.modelLimits = modelLimits;
};
```

## 数据流转

1. **初始化**：从 `props.data.modelLimits` 加载数据
2. **可视化编辑**：通过界面操作直接修改 `formData.value.modelLimits`
3. **JSON编辑**：解析JSON后直接赋值给 `formData.value.modelLimits`
4. **保存**：`formData.value.modelLimits` 随表单一起提交

## 状态显示

根据 `modelLimits` 中的配置实时显示状态：

```javascript
// 无限制
if (!modelLimits[modelName]?.per && !modelLimits[modelName]?.limit) {
  return '🟢 无限制使用';
}

// 禁用
if (modelLimits[modelName]?.limit === 0) {
  return '🔴 禁止使用';
}

// 限制使用
if (modelLimits[modelName]?.per && modelLimits[modelName]?.limit) {
  return '🟡 限制使用';
}

// 配置不完整
return '🔵 配置不完整';
```

## 关键要点

1. **数据源唯一**：`formData.value.modelLimits` 是唯一的数据源
2. **实时同步**：可视化和JSON模式都实时反映 `modelLimits` 的变化
3. **格式验证**：确保数据格式正确
4. **状态反馈**：根据配置实时显示状态

## 简化后的优势

1. **逻辑清晰**：专注于 `modelLimits` 字段的处理
2. **数据一致**：避免多个数据源导致的不一致
3. **易于维护**：减少复杂的数据转换逻辑
4. **性能更好**：减少不必要的数据处理

这样的设计确保了无论用户选择哪种编辑方式，最终都能正确生成和处理 `modelLimits` 字段。
