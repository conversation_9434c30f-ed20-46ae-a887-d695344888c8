# 套餐内自定义模型管理功能完整说明

## 功能概述

本次重构实现了在套餐新增/编辑弹窗中完全自定义模型管理的功能，支持：

### 1. 双模式编辑
- **可视化编辑**：直观的界面操作，适合普通用户
- **JSON编辑**：批量配置和高级用户使用，支持快速导入导出

### 2. 完整的模型管理
- 添加自定义模型（模型名称和显示名称）
- 在线编辑模型名称（支持修改模型ID和显示名称）
- 删除不需要的模型
- 为每个模型配置独立的速率限制

### 3. 优化的用户界面
- 卡片式模型展示，美观直观
- 实时状态标签（🟢无限制/🔴禁用/🟡限制使用/🔵配置不完整）
- 实时配置预览和说明
- 友好的操作提示和确认
- 响应式设计和流畅动画效果

## 使用方法

### 访问功能
1. 进入套餐管理页面：`/subtype`
2. 点击"新增订阅"或编辑现有套餐
3. 切换到"模型速率限制"标签页

### 可视化编辑模式

#### 1. 添加模型
1. 点击"添加模型"按钮
2. 填写模型信息：
   - **模型名称**：系统识别的唯一标识（如：gpt-4o）
   - **显示名称**：用户友好的名称（如：GPT-4o）
3. 点击"添加"按钮

#### 2. 编辑模型
1. 点击模型卡片右上角的"编辑"按钮
2. 直接修改模型名称和显示名称
3. 点击"保存"确认修改

#### 3. 配置速率限制
为每个模型设置：
- **限制周期**：1s/1m/1h/3h/1d/1w/1y
- **使用次数**：在指定周期内的最大使用次数

#### 4. 删除模型
点击模型卡片右上角的"删除"按钮，确认后删除

### JSON编辑模式

#### 1. 切换到JSON模式
点击右上角的"JSON编辑"单选按钮

#### 2. 使用JSON模板
点击"使用模板"按钮获取标准JSON格式

#### 3. JSON格式说明
```json
{
  "models": [
    {
      "name": "gpt-4o",
      "displayName": "GPT-4o"
    }
  ],
  "modelLimits": {
    "gpt-4o": {
      "limit": 10,
      "per": "1h"
    }
  }
}
```

#### 4. 应用更改
编辑完JSON后点击"应用更改"按钮

## 配置规则

### 速率限制逻辑
1. **无限制使用**：周期或次数为空
2. **禁止使用**：次数设为0
3. **限制使用**：同时设置周期和次数
4. **配置不完整**：只设置周期或次数（按无限制处理）

### 时间周期选项
- `1s` - 每秒
- `1m` - 每分钟  
- `1h` - 每小时
- `3h` - 每3小时
- `1d` - 每天
- `1w` - 每周
- `1y` - 每年

## 界面特性

### 状态指示
- 🟢 **绿色标签**：无限制使用
- 🔴 **红色标签**：禁止使用
- 🟡 **黄色标签**：限制使用
- 🔵 **蓝色标签**：配置不完整

### 交互效果
- 卡片悬停动画
- 平滑的状态切换
- 实时配置预览
- 友好的错误提示

### 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的操作界面
- 合理的间距和布局

## 数据管理

### 数据持久化
- 模型配置随套餐数据保存
- 编辑时自动加载现有配置
- 复制套餐时同步复制模型配置

### 数据验证
- 模型名称唯一性检查
- JSON格式验证
- 必填字段验证

## 技术特点

### 前端技术
- Vue 3 Composition API
- Element Plus UI组件
- TypeScript类型安全
- 响应式数据绑定

### 用户体验
- 零学习成本的可视化界面
- 高效的JSON批量编辑
- 实时反馈和状态提示
- 防误操作的确认机制

## 优势总结

1. **完全自定义**：不受预设模型限制
2. **双模式支持**：满足不同用户需求
3. **直观易用**：可视化界面降低使用门槛
4. **高效批量**：JSON模式支持快速配置
5. **实时反馈**：配置效果立即可见
6. **数据安全**：完善的验证和确认机制
7. **界面美观**：现代化的设计和动画效果

这个功能让套餐管理更加灵活强大，管理员可以根据实际需求自由配置模型和速率限制，同时提供了适合不同技术水平用户的操作方式。
